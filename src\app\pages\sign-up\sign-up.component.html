<div class="sign-up-container">
  <div class="form-wrapper">
    <!-- Header -->
    <div class="header">
      <div class="logo">
        <div class="logo-icon">👷</div>
        <h1>JobUp</h1>
      </div>
      <h2>Join Our Community!</h2>
      <p>Create your account to start connecting with opportunities and skilled professionals</p>
    </div>

    <!-- Sign Up Form -->
    <form class="sign-up-form" (ngSubmit)="onSignUp()" #signUpForm="ngForm">
      <!-- User Type Selection -->
      <div class="form-group">
        <label class="form-label">
          I want to <span class="required">*</span>
        </label>
        <div class="user-type-selection">
          <label class="user-type-option" [class.selected]="signUpData.userType === 'worker'">
            <input
              type="radio"
              name="userType"
              value="worker"
              [(ngModel)]="signUpData.userType"
              required
            />
            <div class="option-content">
              <span class="option-icon">👷‍♂️</span>
              <div class="option-text">
                <strong>Find Work</strong>
                <small>I'm looking for job opportunities</small>
              </div>
            </div>
          </label>
          <label class="user-type-option" [class.selected]="signUpData.userType === 'employer'">
            <input
              type="radio"
              name="userType"
              value="employer"
              [(ngModel)]="signUpData.userType"
              required
            />
            <div class="option-content">
              <span class="option-icon">🏢</span>
              <div class="option-text">
                <strong>Hire Workers</strong>
                <small>I need to find skilled professionals</small>
              </div>
            </div>
          </label>
        </div>
      </div>

      <!-- Name Fields -->
      <div class="form-row">
        <div class="form-group">
          <label class="form-label" for="firstName">
            First Name <span class="required">*</span>
          </label>
          <div class="input-wrapper">
            <span class="input-icon">👤</span>
            <input
              type="text"
              id="firstName"
              name="firstName"
              class="form-input"
              placeholder="Enter your first name"
              [(ngModel)]="signUpData.firstName"
              required
              minlength="2"
              #firstNameInput="ngModel"
            />
          </div>
          <div class="error-message" *ngIf="firstNameInput.invalid && firstNameInput.touched">
            <span *ngIf="firstNameInput.errors?.['required']">First name is required</span>
            <span *ngIf="firstNameInput.errors?.['minlength']">First name must be at least 2 characters</span>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label" for="lastName">
            Last Name <span class="required">*</span>
          </label>
          <div class="input-wrapper">
            <span class="input-icon">👤</span>
            <input
              type="text"
              id="lastName"
              name="lastName"
              class="form-input"
              placeholder="Enter your last name"
              [(ngModel)]="signUpData.lastName"
              required
              minlength="2"
              #lastNameInput="ngModel"
            />
          </div>
          <div class="error-message" *ngIf="lastNameInput.invalid && lastNameInput.touched">
            <span *ngIf="lastNameInput.errors?.['required']">Last name is required</span>
            <span *ngIf="lastNameInput.errors?.['minlength']">Last name must be at least 2 characters</span>
          </div>
        </div>
      </div>

      <!-- Email Field -->
      <div class="form-group">
        <label class="form-label" for="email">
          Email Address <span class="required">*</span>
        </label>
        <div class="input-wrapper">
          <span class="input-icon">📧</span>
          <input
            type="email"
            id="email"
            name="email"
            class="form-input"
            placeholder="Enter your email address"
            [(ngModel)]="signUpData.email"
            required
            email
            #emailInput="ngModel"
          />
        </div>
        <div class="error-message" *ngIf="emailInput.invalid && emailInput.touched">
          <span *ngIf="emailInput.errors?.['required']">Email is required</span>
          <span *ngIf="emailInput.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <!-- Phone Field -->
      <div class="form-group">
        <label class="form-label" for="phone">
          Phone Number <span class="required">*</span>
        </label>
        <div class="input-wrapper">
          <span class="input-icon">📱</span>
          <input
            type="tel"
            id="phone"
            name="phone"
            class="form-input"
            placeholder="Enter your phone number"
            [(ngModel)]="signUpData.phone"
            required
            pattern="[0-9+\-\s\(\)]+"
            #phoneInput="ngModel"
          />
        </div>
        <div class="error-message" *ngIf="phoneInput.invalid && phoneInput.touched">
          <span *ngIf="phoneInput.errors?.['required']">Phone number is required</span>
          <span *ngIf="phoneInput.errors?.['pattern']">Please enter a valid phone number</span>
        </div>
      </div>

      <!-- Password Fields -->
      <div class="form-group">
        <label class="form-label" for="password">
          Password <span class="required">*</span>
        </label>
        <div class="input-wrapper">
          <span class="input-icon">🔒</span>
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            name="password"
            class="form-input"
            placeholder="Create a strong password"
            [(ngModel)]="signUpData.password"
            required
            minlength="8"
            #passwordInput="ngModel"
          />
          <button
            type="button"
            class="password-toggle"
            (click)="togglePassword()"
          >
            {{ showPassword ? '🙈' : '👁️' }}
          </button>
        </div>
        <div class="password-strength" *ngIf="signUpData.password">
          <div class="strength-bar">
            <div class="strength-fill" [class]="getPasswordStrength()"></div>
          </div>
          <span class="strength-text">{{ getPasswordStrengthText() }}</span>
        </div>
        <div class="error-message" *ngIf="passwordInput.invalid && passwordInput.touched">
          <span *ngIf="passwordInput.errors?.['required']">Password is required</span>
          <span *ngIf="passwordInput.errors?.['minlength']">Password must be at least 8 characters</span>
        </div>
      </div>

      <!-- Confirm Password Field -->
      <div class="form-group">
        <label class="form-label" for="confirmPassword">
          Confirm Password <span class="required">*</span>
        </label>
        <div class="input-wrapper">
          <span class="input-icon">🔒</span>
          <input
            [type]="showConfirmPassword ? 'text' : 'password'"
            id="confirmPassword"
            name="confirmPassword"
            class="form-input"
            placeholder="Confirm your password"
            [(ngModel)]="signUpData.confirmPassword"
            required
            #confirmPasswordInput="ngModel"
          />
          <button
            type="button"
            class="password-toggle"
            (click)="toggleConfirmPassword()"
          >
            {{ showConfirmPassword ? '🙈' : '👁️' }}
          </button>
        </div>
        <div class="error-message" *ngIf="confirmPasswordInput.invalid && confirmPasswordInput.touched || passwordMismatch">
          <span *ngIf="confirmPasswordInput.errors?.['required']">Please confirm your password</span>
          <span *ngIf="passwordMismatch && !confirmPasswordInput.errors?.['required']">Passwords do not match</span>
        </div>
      </div>

      <!-- Terms and Conditions -->
      <div class="form-group">
        <label class="checkbox-wrapper terms-checkbox">
          <input
            type="checkbox"
            [(ngModel)]="acceptTerms"
            name="acceptTerms"
            required
            #termsInput="ngModel"
          />
          <span class="checkmark"></span>
          I agree to the <a href="#" class="terms-link">Terms of Service</a> and <a href="#" class="terms-link">Privacy Policy</a>
        </label>
        <div class="error-message" *ngIf="termsInput.invalid && termsInput.touched">
          <span>You must accept the terms and conditions</span>
        </div>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        class="sign-up-btn"
        [disabled]="signUpForm.invalid || isLoading || passwordMismatch"
      >
        <span *ngIf="!isLoading">Create Account</span>
        <span *ngIf="isLoading" class="loading-spinner">🔄 Creating Account...</span>
      </button>

      <!-- Divider -->
      <div class="divider">
        <span>or</span>
      </div>

      <!-- Social Sign Up -->
      <div class="social-buttons">
        <button type="button" class="social-btn google-btn">
          <span class="social-icon">🔍</span>
          Sign up with Google
        </button>
        <button type="button" class="social-btn linkedin-btn">
          <span class="social-icon">💼</span>
          Sign up with LinkedIn
        </button>
      </div>

      <!-- Sign In Link -->
      <div class="auth-switch">
        <p>Already have an account?
          <a routerLink="/sign-in" class="switch-link">Sign in here</a>
        </p>
      </div>
    </form>
  </div>

  <!-- Background Elements -->
  <div class="background-elements">
    <div class="floating-element element-1">👷‍♀️</div>
    <div class="floating-element element-2">🔧</div>
    <div class="floating-element element-3">⚡</div>
    <div class="floating-element element-4">💼</div>
    <div class="floating-element element-5">🏗️</div>
    <div class="floating-element element-6">🎯</div>
  </div>
</div>
