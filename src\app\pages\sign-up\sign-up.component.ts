import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

interface SignUpData {
  userType: 'worker' | 'employer' | '';
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
}

@Component({
  selector: 'app-sign-up',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './sign-up.component.html',
  styleUrl: './sign-up.component.css'
})
export class SignUpComponent {
  signUpData: SignUpData = {
    userType: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  };

  showPassword = false;
  showConfirmPassword = false;
  acceptTerms = false;
  isLoading = false;

  get passwordMismatch(): boolean {
    return this.signUpData.password !== this.signUpData.confirmPassword &&
           this.signUpData.confirmPassword.length > 0;
  }

  togglePassword(): void {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPassword(): void {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  getPasswordStrength(): string {
    const password = this.signUpData.password;
    if (!password) return '';

    let strength = 0;

    // Length check
    if (password.length >= 8) strength++;
    if (password.length >= 12) strength++;

    // Character variety checks
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    if (strength <= 2) return 'weak';
    if (strength <= 4) return 'medium';
    return 'strong';
  }

  getPasswordStrengthText(): string {
    const strength = this.getPasswordStrength();
    switch (strength) {
      case 'weak': return 'Weak password';
      case 'medium': return 'Medium strength';
      case 'strong': return 'Strong password';
      default: return '';
    }
  }

  onSignUp(): void {
    if (this.isLoading || this.passwordMismatch) return;

    this.isLoading = true;

    // Simulate API call
    setTimeout(() => {
      console.log('Sign up attempt:', this.signUpData);
      // Here you would typically call your authentication service
      // this.authService.signUp(this.signUpData).subscribe(...)

      this.isLoading = false;

      // For demo purposes, show success message
      alert('Account creation functionality will be implemented with your authentication service!');
    }, 2000);
  }
}
