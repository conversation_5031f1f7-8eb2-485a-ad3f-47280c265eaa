import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule, Router } from '@angular/router';
import { AuthControllerService } from '../../generated-sources/openapi/api/authController.service';
import { RegisterRequestDto, AuthResponseDto } from '../../generated-sources/openapi/model/models';
import { AuthService } from '../../services/auth.service';

interface SignUpData extends RegisterRequestDto {
  userType: 'worker' | 'employer' | '';
  firstName: string;
  lastName: string;
  phone: string;
  confirmPassword: string;
}

@Component({
  selector: 'app-sign-up',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './sign-up.component.html',
  styleUrl: './sign-up.component.css'
})
export class SignUpComponent {
  signUpData: SignUpData = {
    userType: '',
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  };

  showPassword = false;
  showConfirmPassword = false;
  acceptTerms = false;
  isLoading = false;
  errorMessage = '';
  successMessage = '';

  constructor(
    private authControllerService: AuthControllerService,
    private authService: AuthService,
    private router: Router
  ) {}

  get passwordMismatch(): boolean {
    return this.signUpData.password !== this.signUpData.confirmPassword &&
           this.signUpData.confirmPassword.length > 0;
  }

  togglePassword(): void {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPassword(): void {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  getPasswordStrength(): string {
    const password = this.signUpData.password;
    if (!password) return '';

    let strength = 0;

    // Length check
    if (password.length >= 8) strength++;
    if (password.length >= 12) strength++;

    // Character variety checks
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    if (strength <= 2) return 'weak';
    if (strength <= 4) return 'medium';
    return 'strong';
  }

  getPasswordStrengthText(): string {
    const strength = this.getPasswordStrength();
    switch (strength) {
      case 'weak': return 'Weak password';
      case 'medium': return 'Medium strength';
      case 'strong': return 'Strong password';
      default: return '';
    }
  }

  onSignUp(): void {
    if (this.isLoading || this.passwordMismatch) return;

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    // Create the register request DTO
    const registerRequest: RegisterRequestDto = {
      username: this.generateUsername(),
      email: this.signUpData.email,
      password: this.signUpData.password
    };

    this.authControllerService.register(registerRequest).subscribe({
      next: (response: AuthResponseDto) => {
        console.log('Registration successful:', response);

        // Use the auth service to store user data
        if (response.token) {
          this.authService.setUser(response.token, response.roles || []);
        }

        this.isLoading = false;
        this.successMessage = 'Account created successfully! Redirecting...';

        // Navigate to dashboard or worker list after a short delay
        setTimeout(() => {
          this.router.navigate(['/workers']);
        }, 2000);
      },
      error: (error: any) => {
        console.error('Registration failed:', error);
        this.isLoading = false;

        // Handle different error scenarios
        if (error.status === 409) {
          this.errorMessage = 'An account with this email already exists.';
        } else if (error.status === 400) {
          this.errorMessage = 'Please check your information and try again.';
        } else if (error.status === 0) {
          this.errorMessage = 'Unable to connect to server. Please check your connection.';
        } else {
          this.errorMessage = 'An error occurred during registration. Please try again.';
        }
      }
    });
  }

  private generateUsername(): string {
    // Generate a username from first name, last name, and email
    const firstName = this.signUpData.firstName.toLowerCase().replace(/\s+/g, '');
    const lastName = this.signUpData.lastName.toLowerCase().replace(/\s+/g, '');
    const emailPrefix = this.signUpData.email?.split('@')[0] || '';

    // Try different combinations
    if (firstName && lastName) {
      return `${firstName}.${lastName}`;
    } else if (emailPrefix) {
      return emailPrefix;
    } else {
      return `user${Date.now()}`;
    }
  }
}
