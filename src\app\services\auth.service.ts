import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';

export interface User {
  token: string;
  roles: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(private router: Router) {
    // Check if user is already logged in on service initialization
    this.loadUserFromStorage();
  }

  private loadUserFromStorage(): void {
    const token = localStorage.getItem('authToken');
    const roles = localStorage.getItem('userRoles');
    
    if (token && roles) {
      const user: User = {
        token,
        roles: JSON.parse(roles)
      };
      this.currentUserSubject.next(user);
    }
  }

  public get currentUserValue(): User | null {
    return this.currentUserSubject.value;
  }

  public get isAuthenticated(): boolean {
    return !!this.currentUserValue?.token;
  }

  public get userRoles(): string[] {
    return this.currentUserValue?.roles || [];
  }

  public hasRole(role: string): boolean {
    return this.userRoles.includes(role);
  }

  public setUser(token: string, roles: string[]): void {
    const user: User = { token, roles };
    
    // Store in localStorage
    localStorage.setItem('authToken', token);
    localStorage.setItem('userRoles', JSON.stringify(roles));
    
    // Update current user
    this.currentUserSubject.next(user);
  }

  public logout(): void {
    // Remove from localStorage
    localStorage.removeItem('authToken');
    localStorage.removeItem('userRoles');
    
    // Clear current user
    this.currentUserSubject.next(null);
    
    // Navigate to sign-in
    this.router.navigate(['/sign-in']);
  }

  public getAuthToken(): string | null {
    return this.currentUserValue?.token || null;
  }
}
