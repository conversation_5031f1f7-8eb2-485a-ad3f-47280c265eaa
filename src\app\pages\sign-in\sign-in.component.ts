import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule, Router } from '@angular/router';
import { AuthControllerService } from '../../generated-sources/openapi/api/authController.service';
import { LoginRequestDto, AuthResponseDto } from '../../generated-sources/openapi/model/models';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-sign-in',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './sign-in.component.html',
  styleUrl: './sign-in.component.css'
})
export class SignInComponent {
  signInData: LoginRequestDto = {
    username: '',
    password: ''
  };

  showPassword = false;
  rememberMe = false;
  isLoading = false;
  errorMessage = '';

  constructor(
    private authControllerService: AuthControllerService,
    private authService: AuthService,
    private router: Router
  ) {}

  togglePassword(): void {
    this.showPassword = !this.showPassword;
  }

  onSignIn(): void {
    if (this.isLoading) return;

    this.isLoading = true;
    this.errorMessage = '';

    this.authControllerService.login(this.signInData).subscribe({
      next: (response: AuthResponseDto) => {
        console.log('Login successful:', response);

        // Use the auth service to store user data
        if (response.token) {
          this.authService.setUser(response.token, response.roles || []);
        }

        this.isLoading = false;

        // Navigate to dashboard or worker list
        this.router.navigate(['/workers']);
      },
      error: (error: any) => {
        console.error('Login failed:', error);
        this.isLoading = false;

        // Handle different error scenarios
        if (error.status === 401) {
          this.errorMessage = 'Invalid username or password. Please try again.';
        } else if (error.status === 0) {
          this.errorMessage = 'Unable to connect to server. Please check your connection.';
        } else {
          this.errorMessage = 'An error occurred during sign in. Please try again.';
        }
      }
    });
  }
}
