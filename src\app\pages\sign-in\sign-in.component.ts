import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

interface SignInData {
  email: string;
  password: string;
}

@Component({
  selector: 'app-sign-in',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './sign-in.component.html',
  styleUrl: './sign-in.component.css'
})
export class SignInComponent {
  signInData: SignInData = {
    email: '',
    password: ''
  };

  showPassword = false;
  rememberMe = false;
  isLoading = false;

  togglePassword(): void {
    this.showPassword = !this.showPassword;
  }

  onSignIn(): void {
    if (this.isLoading) return;

    this.isLoading = true;

    // Simulate API call
    setTimeout(() => {
      console.log('Sign in attempt:', this.signInData);
      // Here you would typically call your authentication service
      // this.authService.signIn(this.signInData).subscribe(...)

      this.isLoading = false;

      // For demo purposes, show success message
      alert('Sign in functionality will be implemented with your authentication service!');
    }, 2000);
  }
}
