<div class="worker-edit-container">
  <!-- Navigation -->
  <div class="navigation">
    <button class="back-btn" (click)="goBack()">
      <i class="back-icon">←</i>
      Back to Profile
    </button>
  </div>

  <div class="form-wrapper">
    <div class="header">
      <h1>Edit Worker Profile</h1>
      <p>Update your profile information to keep it current and accurate.</p>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading worker data...</p>
    </div>

    <!-- Load Error Message -->
    <div *ngIf="loadError" class="alert alert-error">
      <i class="error-icon">⚠</i>
      {{ loadError }}
      <button class="retry-btn" (click)="loadWorkerData()">Retry</button>
    </div>

    <!-- Success Message -->
    <div *ngIf="submitSuccess" class="alert alert-success">
      <i class="success-icon">✓</i>
      Worker profile updated successfully! Redirecting to profile page...
    </div>

    <!-- Submit Error Message -->
    <div *ngIf="submitError" class="alert alert-error">
      <i class="error-icon">⚠</i>
      {{ submitError }}
    </div>

    <!-- Edit Form -->
    <form *ngIf="!isLoading && !loadError" [formGroup]="workerForm" (ngSubmit)="onSubmit()" class="worker-form">
      <!-- Full Name Field -->
      <div class="form-group">
        <label for="fullName" class="form-label">
          Full Name <span class="required">*</span>
        </label>
        <input
          type="text"
          id="fullName"
          formControlName="fullName"
          class="form-input"
          [class.error]="isFieldInvalid('fullName')"
          placeholder="Enter your full name"
        />
        <div *ngIf="isFieldInvalid('fullName')" class="error-message">
          {{ getFieldError('fullName') }}
        </div>
      </div>

      <!-- Job Type Field -->
      <div class="form-group">
        <label for="jobType" class="form-label">
          Job Type <span class="required">*</span>
        </label>
        <select
          id="jobType"
          formControlName="jobType"
          class="form-select"
          [class.error]="isFieldInvalid('jobType')"
        >
          <option value="">Select your job type</option>
          <option *ngFor="let jobType of jobTypes" [value]="jobType">
            {{ jobType }}
          </option>
        </select>
        <div *ngIf="isFieldInvalid('jobType')" class="error-message">
          {{ getFieldError('jobType') }}
        </div>
      </div>

      <!-- Phone Number Field -->
      <div class="form-group">
        <label for="phoneNumber" class="form-label">
          Phone Number <span class="required">*</span>
        </label>
        <input
          type="tel"
          id="phoneNumber"
          formControlName="phoneNumber"
          class="form-input"
          [class.error]="isFieldInvalid('phoneNumber')"
          placeholder="Enter your phone number"
        />
        <div *ngIf="isFieldInvalid('phoneNumber')" class="error-message">
          {{ getFieldError('phoneNumber') }}
        </div>
      </div>

      <!-- Location Field -->
      <div class="form-group">
        <label for="location" class="form-label">
          Location <span class="required">*</span>
        </label>
        <input
          type="text"
          id="location"
          formControlName="location"
          class="form-input"
          [class.error]="isFieldInvalid('location')"
          placeholder="Enter your city or area"
        />
        <div *ngIf="isFieldInvalid('location')" class="error-message">
          {{ getFieldError('location') }}
        </div>
      </div>

      <!-- Description Field -->
      <div class="form-group">
        <label for="description" class="form-label">
          Description <span class="required">*</span>
        </label>
        <textarea
          id="description"
          formControlName="description"
          class="form-textarea"
          [class.error]="isFieldInvalid('description')"
          placeholder="Describe your skills, experience, and services you offer..."
          rows="4"
        ></textarea>
        <div *ngIf="isFieldInvalid('description')" class="error-message">
          {{ getFieldError('description') }}
        </div>
      </div>

      <!-- Form Actions -->
      <div class="form-actions">
        <button
          type="button"
          class="cancel-btn"
          (click)="goBack()"
          [disabled]="isSubmitting"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="submit-btn"
          [disabled]="isSubmitting"
          [class.loading]="isSubmitting"
        >
          <span *ngIf="!isSubmitting">Update Profile</span>
          <span *ngIf="isSubmitting">
            <i class="loading-spinner"></i>
            Updating Profile...
          </span>
        </button>
      </div>
    </form>
  </div>
</div>
