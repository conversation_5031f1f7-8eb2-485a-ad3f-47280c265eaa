import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'workers',
    loadComponent: () => import('./pages/worker-list/worker-list.component').then(m => m.WorkerListComponent)
  },
  {
    path: 'workers/create',
    loadComponent: () => import('./pages/create-worker/create-worker.component').then(m => m.CreateWorkerComponent)
  },
  {
    path: 'workers/:id',
    loadComponent: () => import('./pages/worker-detail/worker-detail.component').then(m => m.WorkerDetailComponent)
  },
  {
    path: 'workers/:id/edit',
    loadComponent: () => import('./pages/worker-edit/worker-edit.component').then(m => m.WorkerEditComponent)
  },
  {
    path: '',
    redirectTo: '/workers',
    pathMatch: 'full'
  }
];
