import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'sign-in',
    loadComponent: () => import('./pages/sign-in/sign-in.component').then(m => m.SignInComponent)
  },
  {
    path: 'sign-up',
    loadComponent: () => import('./pages/sign-up/sign-up.component').then(m => m.SignUpComponent)
  },
  {
    path: 'workers',
    loadComponent: () => import('./pages/worker-list/worker-list.component').then(m => m.WorkerListComponent)
  },
  {
    path: 'workers/create',
    loadComponent: () => import('./pages/create-worker/create-worker.component').then(m => m.CreateWorkerComponent)
  },
  {
    path: 'workers/:id',
    loadComponent: () => import('./pages/worker-detail/worker-detail.component').then(m => m.WorkerDetailComponent)
  },
  {
    path: 'workers/:id/edit',
    loadComponent: () => import('./pages/worker-edit/worker-edit.component').then(m => m.WorkerEditComponent)
  },
  {
    path: '',
    redirectTo: '/sign-in',
    pathMatch: 'full'
  }
];
