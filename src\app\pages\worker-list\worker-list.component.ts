import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { WorkerControllerService, WorkerResponseDto } from '../../generated-sources/openapi';

@Component({
  selector: 'app-worker-list',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './worker-list.component.html',
  styleUrl: './worker-list.component.css'
})
export class WorkerListComponent implements OnInit {
  private fb = inject(FormBuilder);
  private workerService = inject(WorkerControllerService);
  private router = inject(Router);

  searchForm: FormGroup;
  workers: WorkerResponseDto[] = [];
  filteredWorkers: WorkerResponseDto[] = [];
  isLoading = false;
  error: string | null = null;

  // Job types for search filter
  jobTypes = [
    'All',
    'Plumber',
    'Electrician',
    '<PERSON>',
    'Painter',
    'Cleaner',
    'Gardener',
    'Mechanic',
    '<PERSON>',
    'Driver',
    '<PERSON>yman',
    'Other'
  ];

  constructor() {
    this.searchForm = this.fb.group({
      jobType: ['All'],
      location: ['']
    });

    // Subscribe to form changes for real-time filtering
    this.searchForm.valueChanges.subscribe(() => {
      this.filterWorkers();
    });
  }

  ngOnInit(): void {
    this.loadAllWorkers();
  }

  loadAllWorkers(): void {
    this.isLoading = true;
    this.error = null;

    this.workerService.getAllWorkers().subscribe({
      next: (workers) => {
        this.workers = workers;
        this.filteredWorkers = workers;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading workers:', error);
        this.error = 'Failed to load workers. Please try again.';
        this.isLoading = false;
      }
    });
  }

  filterWorkers(): void {
    const { jobType, location } = this.searchForm.value;

    this.filteredWorkers = this.workers.filter(worker => {
      const matchesJobType = jobType === 'All' || worker.jobType === jobType;
      const matchesLocation = !location ||
        (worker.location && worker.location.toLowerCase().includes(location.toLowerCase()));

      return matchesJobType && matchesLocation;
    });
  }

  searchByJobType(): void {
    const jobType = this.searchForm.get('jobType')?.value;
    if (jobType && jobType !== 'All') {
      this.isLoading = true;
      this.workerService.searchByJobType(jobType).subscribe({
        next: (workers) => {
          this.workers = workers;
          this.filterWorkers();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error searching by job type:', error);
          this.error = 'Failed to search workers. Please try again.';
          this.isLoading = false;
        }
      });
    } else {
      this.loadAllWorkers();
    }
  }

  searchByLocation(): void {
    const location = this.searchForm.get('location')?.value;
    if (location) {
      this.isLoading = true;
      this.workerService.searchByLocation(location).subscribe({
        next: (workers) => {
          this.workers = workers;
          this.filterWorkers();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error searching by location:', error);
          this.error = 'Failed to search workers. Please try again.';
          this.isLoading = false;
        }
      });
    } else {
      this.loadAllWorkers();
    }
  }

  viewWorkerDetail(workerId: string | undefined): void {
    if (workerId) {
      this.router.navigate(['/workers', workerId]);
    }
  }

  createNewWorker(): void {
    this.router.navigate(['/workers/create']);
  }

  clearSearch(): void {
    this.searchForm.reset({
      jobType: 'All',
      location: ''
    });
    this.loadAllWorkers();
  }

  getStarRating(rating: number | undefined): string[] {
    const stars = [];
    const ratingValue = rating || 0;

    for (let i = 1; i <= 5; i++) {
      if (i <= ratingValue) {
        stars.push('★');
      } else {
        stars.push('☆');
      }
    }
    return stars;
  }
}
