.sign-up-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.form-wrapper {
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  padding: 3rem 2.5rem;
  width: 100%;
  max-width: 500px;
  position: relative;
  z-index: 10;
  backdrop-filter: blur(10px);
  max-height: 90vh;
  overflow-y: auto;
}

/* Header Styles */
.header {
  text-align: center;
  margin-bottom: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.logo-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo h1 {
  color: #2d3748;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header h2 {
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.header p {
  color: #718096;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

/* Alert Styles */
.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.alert-success {
  background-color: #f0fff4;
  border: 1px solid #9ae6b4;
  color: #276749;
}

.alert-error {
  background-color: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
}

.success-icon,
.error-icon {
  font-weight: bold;
  font-size: 1.2rem;
}

/* Form Styles */
.sign-up-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-label {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.required {
  color: #e53e3e;
}

/* User Type Selection */
.user-type-selection {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.user-type-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.user-type-option:hover {
  border-color: #cbd5e0;
  transform: translateY(-1px);
}

.user-type-option.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.user-type-option input[type="radio"] {
  display: none;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.option-icon {
  font-size: 1.5rem;
}

.option-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.option-text strong {
  color: #2d3748;
  font-size: 1rem;
}

.option-text small {
  color: #718096;
  font-size: 0.85rem;
}

/* Input Styles */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  font-size: 1rem;
  z-index: 2;
  opacity: 0.7;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 2.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.form-input::placeholder {
  color: #a0aec0;
  font-size: 0.9rem;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  z-index: 2;
}

.password-toggle:hover {
  background: rgba(102, 126, 234, 0.1);
}

/* Password Strength Indicator */
.password-strength {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  width: 33%;
  background: #e53e3e;
}

.strength-fill.medium {
  width: 66%;
  background: #ed8936;
}

.strength-fill.strong {
  width: 100%;
  background: #38a169;
}

.strength-text {
  font-size: 0.8rem;
  font-weight: 500;
  min-width: 100px;
}

.strength-text:empty {
  display: none;
}

/* Error Messages */
.error-message {
  color: #e53e3e;
  font-size: 0.8rem;
  margin-top: 0.4rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Terms Checkbox */
.terms-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.85rem;
  color: #4a5568;
  line-height: 1.4;
}

.terms-checkbox input[type="checkbox"] {
  width: 1.1rem;
  height: 1.1rem;
  accent-color: #667eea;
  margin-top: 0.1rem;
}

.terms-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.terms-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* Sign Up Button */
.sign-up-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.sign-up-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
}

.sign-up-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: 1.25rem 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e2e8f0;
}

.divider span {
  background: white;
  padding: 0 1rem;
  color: #718096;
  font-size: 0.85rem;
  position: relative;
  z-index: 1;
}

/* Social Buttons */
.social-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 0.8rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  background: white;
  color: #4a5568;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.social-btn:hover {
  border-color: #cbd5e0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.google-btn:hover {
  border-color: #ea4335;
  color: #ea4335;
}

.linkedin-btn:hover {
  border-color: #0077b5;
  color: #0077b5;
}

.social-icon {
  font-size: 1.1rem;
}

/* Auth Switch */
.auth-switch {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.auth-switch p {
  color: #718096;
  font-size: 0.95rem;
  margin: 0;
}

.switch-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s ease;
}

.switch-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* Background Elements */
.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-element {
  position: absolute;
  font-size: 2rem;
  opacity: 0.1;
  animation: float 6s ease-in-out infinite;
}

.element-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.element-3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

.element-4 {
  bottom: 20%;
  right: 10%;
  animation-delay: 3s;
}

.element-5 {
  top: 60%;
  left: 5%;
  animation-delay: 4s;
}

.element-6 {
  top: 40%;
  right: 25%;
  animation-delay: 5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(10deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sign-up-container {
    padding: 1rem;
  }

  .form-wrapper {
    padding: 2rem 1.5rem;
    max-width: 100%;
    max-height: 95vh;
  }

  .logo h1 {
    font-size: 1.75rem;
  }

  .header h2 {
    font-size: 1.25rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .social-buttons {
    gap: 0.5rem;
  }

  .user-type-selection {
    gap: 0.5rem;
  }

  .user-type-option {
    padding: 0.75rem;
  }
}

@media (max-width: 480px) {
  .form-wrapper {
    padding: 1.5rem 1rem;
  }

  .logo {
    flex-direction: column;
    gap: 0.5rem;
  }

  .logo-icon {
    font-size: 2rem;
  }

  .logo h1 {
    font-size: 1.5rem;
  }

  .header h2 {
    font-size: 1.1rem;
  }

  .floating-element {
    font-size: 1.5rem;
  }

  .option-content {
    gap: 0.75rem;
  }

  .option-icon {
    font-size: 1.25rem;
  }
}

/* Password Strength Indicator */
.password-strength {
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  width: 33%;
  background: #e53e3e;
}

.strength-fill.medium {
  width: 66%;
  background: #ed8936;
}

.strength-fill.strong {
  width: 100%;
  background: #38a169;
}

.strength-text {
  font-size: 0.8rem;
  font-weight: 500;
  min-width: 100px;
}

.strength-text:empty {
  display: none;
}

/* Error Messages */
.error-message {
  color: #e53e3e;
  font-size: 0.8rem;
  margin-top: 0.4rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Terms Checkbox */
.terms-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.85rem;
  color: #4a5568;
  line-height: 1.4;
}

.terms-checkbox input[type="checkbox"] {
  width: 1.1rem;
  height: 1.1rem;
  accent-color: #667eea;
  margin-top: 0.1rem;
}

.terms-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.terms-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* Sign Up Button */
.sign-up-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.sign-up-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
}

.sign-up-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Divider */
.divider {
  position: relative;
  text-align: center;
  margin: 1.25rem 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e2e8f0;
}

.divider span {
  background: white;
  padding: 0 1rem;
  color: #718096;
  font-size: 0.85rem;
  position: relative;
  z-index: 1;
}

/* Social Buttons */
.social-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 0.8rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  background: white;
  color: #4a5568;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.social-btn:hover {
  border-color: #cbd5e0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.google-btn:hover {
  border-color: #ea4335;
  color: #ea4335;
}

.linkedin-btn:hover {
  border-color: #0077b5;
  color: #0077b5;
}

.social-icon {
  font-size: 1.1rem;
}