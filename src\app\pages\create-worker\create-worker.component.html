<div class="create-worker-container">
  <div class="form-wrapper">
    <div class="header">
      <h1>Create Worker Profile</h1>
      <p>Fill out the form below to create your worker profile and start connecting with potential clients.</p>
    </div>

    <!-- Success Message -->
    <div *ngIf="submitSuccess" class="alert alert-success">
      <i class="success-icon">✓</i>
      Worker profile created successfully! You can create another profile or the form will reset automatically.
    </div>

    <!-- Error Message -->
    <div *ngIf="submitError" class="alert alert-error">
      <i class="error-icon">⚠</i>
      {{ submitError }}
    </div>

    <form [formGroup]="workerForm" (ngSubmit)="onSubmit()" class="worker-form">
      <!-- Full Name Field -->
      <div class="form-group">
        <label for="fullName" class="form-label">
          Full Name <span class="required">*</span>
        </label>
        <input
          type="text"
          id="fullName"
          formControlName="fullName"
          class="form-input"
          [class.error]="isFieldInvalid('fullName')"
          placeholder="Enter your full name"
        />
        <div *ngIf="isFieldInvalid('fullName')" class="error-message">
          {{ getFieldError('fullName') }}
        </div>
      </div>

      <!-- Job Type Field -->
      <div class="form-group">
        <label for="jobType" class="form-label">
          Job Type <span class="required">*</span>
        </label>
        <select
          id="jobType"
          formControlName="jobType"
          class="form-select"
          [class.error]="isFieldInvalid('jobType')"
        >
          <option value="">Select your job type</option>
          <option *ngFor="let jobType of jobTypes" [value]="jobType">
            {{ jobType }}
          </option>
        </select>
        <div *ngIf="isFieldInvalid('jobType')" class="error-message">
          {{ getFieldError('jobType') }}
        </div>
      </div>

      <!-- Phone Number Field -->
      <div class="form-group">
        <label for="phoneNumber" class="form-label">
          Phone Number <span class="required">*</span>
        </label>
        <input
          type="tel"
          id="phoneNumber"
          formControlName="phoneNumber"
          class="form-input"
          [class.error]="isFieldInvalid('phoneNumber')"
          placeholder="Enter your phone number"
        />
        <div *ngIf="isFieldInvalid('phoneNumber')" class="error-message">
          {{ getFieldError('phoneNumber') }}
        </div>
      </div>

      <!-- Location Field -->
      <div class="form-group">
        <label for="location" class="form-label">
          Location <span class="required">*</span>
        </label>
        <input
          type="text"
          id="location"
          formControlName="location"
          class="form-input"
          [class.error]="isFieldInvalid('location')"
          placeholder="Enter your city or area"
        />
        <div *ngIf="isFieldInvalid('location')" class="error-message">
          {{ getFieldError('location') }}
        </div>
      </div>

      <!-- Description Field -->
      <div class="form-group">
        <label for="description" class="form-label">
          Description <span class="required">*</span>
        </label>
        <textarea
          id="description"
          formControlName="description"
          class="form-textarea"
          [class.error]="isFieldInvalid('description')"
          placeholder="Describe your skills, experience, and services you offer..."
          rows="4"
        ></textarea>
        <div *ngIf="isFieldInvalid('description')" class="error-message">
          {{ getFieldError('description') }}
        </div>
      </div>

      <!-- Submit Button -->
      <div class="form-actions">
        <button
          type="submit"
          class="submit-btn"
          [disabled]="isSubmitting"
          [class.loading]="isSubmitting"
        >
          <span *ngIf="!isSubmitting">Create Profile</span>
          <span *ngIf="isSubmitting">
            <i class="loading-spinner"></i>
            Creating Profile...
          </span>
        </button>
      </div>
    </form>
  </div>
</div>
