<div class="sign-in-container">
  <div class="form-wrapper">
    <!-- Header -->
    <div class="header">
      <div class="logo">
        <div class="logo-icon">👷</div>
        <h1>JobUp</h1>
      </div>
      <h2>Welcome Back!</h2>
      <p>Sign in to find your next opportunity or connect with skilled workers</p>
    </div>

    <!-- Error Message -->
    <div class="alert alert-error" *ngIf="errorMessage">
      <span class="error-icon">⚠️</span>
      {{ errorMessage }}
    </div>

    <!-- Sign In Form -->
    <form class="sign-in-form" (ngSubmit)="onSignIn()" #signInForm="ngForm">
      <!-- Username Field -->
      <div class="form-group">
        <label class="form-label" for="username">
          Username or Email <span class="required">*</span>
        </label>
        <div class="input-wrapper">
          <span class="input-icon">👤</span>
          <input
            type="text"
            id="username"
            name="username"
            class="form-input"
            placeholder="Enter your username or email"
            [(ngModel)]="signInData.username"
            required
            #usernameInput="ngModel"
          />
        </div>
        <div class="error-message" *ngIf="usernameInput.invalid && usernameInput.touched">
          <span *ngIf="usernameInput.errors?.['required']">Username or email is required</span>
        </div>
      </div>

      <!-- Password Field -->
      <div class="form-group">
        <label class="form-label" for="password">
          Password <span class="required">*</span>
        </label>
        <div class="input-wrapper">
          <span class="input-icon">🔒</span>
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            name="password"
            class="form-input"
            placeholder="Enter your password"
            [(ngModel)]="signInData.password"
            required
            minlength="6"
            #passwordInput="ngModel"
          />
          <button
            type="button"
            class="password-toggle"
            (click)="togglePassword()"
          >
            {{ showPassword ? '🙈' : '👁️' }}
          </button>
        </div>
        <div class="error-message" *ngIf="passwordInput.invalid && passwordInput.touched">
          <span *ngIf="passwordInput.errors?.['required']">Password is required</span>
          <span *ngIf="passwordInput.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <!-- Remember Me & Forgot Password -->
      <div class="form-options">
        <label class="checkbox-wrapper">
          <input
            type="checkbox"
            [(ngModel)]="rememberMe"
            name="rememberMe"
          />
          <span class="checkmark"></span>
          Remember me
        </label>
        <a href="#" class="forgot-password">Forgot Password?</a>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        class="sign-in-btn"
        [disabled]="signInForm.invalid || isLoading"
      >
        <span *ngIf="!isLoading">Sign In</span>
        <span *ngIf="isLoading" class="loading-spinner">🔄 Signing In...</span>
      </button>

      <!-- Divider -->
      <div class="divider">
        <span>or</span>
      </div>

      <!-- Social Sign In -->
      <div class="social-buttons">
        <button type="button" class="social-btn google-btn">
          <span class="social-icon">🔍</span>
          Continue with Google
        </button>
        <button type="button" class="social-btn linkedin-btn">
          <span class="social-icon">💼</span>
          Continue with LinkedIn
        </button>
      </div>

      <!-- Sign Up Link -->
      <div class="auth-switch">
        <p>Don't have an account?
          <a routerLink="/sign-up" class="switch-link">Sign up here</a>
        </p>
      </div>
    </form>
  </div>

  <!-- Background Elements -->
  <div class="background-elements">
    <div class="floating-element element-1">👷‍♂️</div>
    <div class="floating-element element-2">🔧</div>
    <div class="floating-element element-3">⚡</div>
    <div class="floating-element element-4">💼</div>
    <div class="floating-element element-5">🏗️</div>
  </div>
</div>
